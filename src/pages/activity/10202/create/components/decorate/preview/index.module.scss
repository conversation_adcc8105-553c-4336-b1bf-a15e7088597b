// 为当前页面的 phone-preview-content 添加隐藏滚动条样式
:global(.phone-preview-content) {
  // 隐藏滚动条但保留滚动功能
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE/Edge

  &::-webkit-scrollbar {
    display: none; // Chrome/Safari/Opera
  }
}

.main {
  position: relative;
  background-color: var(--actBgColor);

  .kv {
    width: 100%;
  }

  .btns {
    position: absolute;
    right: -1px;
    top: 140px;

    div {
      background-image: var(--ruleBtn);
      background-repeat: no-repeat;
      background-size: 100%;
      color: var(--ruleBtnColor);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      margin-bottom: 8px;
      padding: 3px 0 0 8px;
      font-size: 10px;
      width: 50px;
      height: 22px;
    }
  }

  .userInfoBg {
    width: 98%;
    height: 90px;
    background-image: var(--userInfoBg);
    background-repeat: no-repeat;
    background-size: 100%;
    padding: 20px;

    .userInfo{
      font-size: 11px;
      color: var(--userInfoColor);
      transform: rotate(-3deg);
    }
  }

  .step1Bg {
    background-image: var(--step1Bg);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 280px;
    height: 210px;
    margin: 10px auto 0;
    padding: 70px 6px 0 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #cc0607;

    .tabContainer {
      width: 50px;
      height: 80px;
      margin-top: 5px;
    }
  }

  .step2Bg {
    width: 100%;
    height: 100%;
    margin: 10px auto 0;
    position: relative;

    .getDemoPrizeBtn {
      background-image: var(--getDemoPrizeBtn);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 80px;
      height: 26px;
      cursor: pointer;
      transition: all 0.3s ease;
      flex-shrink: 0; // 防止tab被压缩

      .stepText {
        color: #fdf5bd;
        margin: 0 auto;
        text-align: center;
        line-height: 22px;
        font-size: 12px;
      }
    }
  }

  .step3Bg {
    background-image: var(--step3Bg);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 260px;
    height: 90px;
    margin: 0 auto;
  }

  .bottomToTop {
    background-image: var(--bottomToTop);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 280px;
    height: 44px;
    margin: 0 auto;
  }
}