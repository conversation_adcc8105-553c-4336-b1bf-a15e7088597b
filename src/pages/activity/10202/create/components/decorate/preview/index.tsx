import PhonePreview from '@/components/PhonePreview/PhonePreview';
import styles from './index.module.scss';
import { createTurnTableMainStyles } from './utils/styleUtils';
import { useEffect, useState } from 'react';

export default function TurnTablePreview(props) {
  console.log('=== TurnTablePreview 组件开始渲染 ===');
  const { tState, state } = props;
  const main = tState;
  const [showDemoSkuList, setShowDemoSkuList] = useState<any>([]);

  console.log('TurnTablePreview props:', props);
  console.log('TurnTablePreview state:', state);
  console.log('TurnTablePreview state.demoSkuList:', state?.demoSkuList);

  // 生成actTitle的函数
  const generateActTitle = (sectionSort: number) => {
    if (sectionSort === 2) {
      return '1段转2段';
    } else if (sectionSort === 3) {
      return '1/2段转3段';
    } else {
      return `转${sectionSort}段`;
    }
  };

  // 将demoSkuList数据按段数分组
  const transformDemoSkuList = (originalData: any[]) => {
    console.log('transformDemoSkuList 开始处理数据:', originalData);
    if (!originalData || !Array.isArray(originalData)) {
      console.log('数据为空或不是数组，返回空数组');
      return [];
    }

    // 使用Map来按sectionSort分组
    const groupedData = new Map();

    originalData.forEach(item => {
      const { sectionNum } = item;
      console.log('处理商品项:', item, '段数:', sectionNum);

      if (!groupedData.has(sectionNum)) {
        const groupData = {
          goodsLineName: item.goodsLine, // 使用第一个商品的品线名作为显示名称
          sectionSort: sectionNum,
          actTitle: generateActTitle(sectionNum), // 生成对应的actTitle
          actNum: sectionNum, // 段数编号
          skuList: [],
        };
        console.log('创建新分组:', groupData);
        groupedData.set(sectionNum, groupData);
      }

      // 为每个sku添加price属性
      let price = '';
      if (item.skuName) {
        // 从skuName中提取价格，以'元'为界限
        const priceMatch = item.skuName.match(/^([^元]+)元/);
        if (priceMatch) {
          price = priceMatch[1];
        }
      }

      // 添加sku信息到对应的段数
      groupedData.get(sectionNum).skuList.push({
        skuId: item.skuId,
        skuMainPicture: item.skuMainPicture,
        skuName: item.skuName,
        prizeMainName: item.prizeMainName,
        dayLimitCount: item.dayLimitCount,
        daylimit: item.daylimit,
        prizeList: item.prizeList,
        sectionSort: item.sectionNum,
        price: price,
      });
    });

    // 转换Map为数组，并按sectionSort排序
    const result = Array.from(groupedData.values()).sort((a, b) => a.actNum - b.actNum);
    console.log('转换完成，最终结果:', result);
    return result;
  };

  useEffect(() => {
    console.log('=== useEffect 开始执行 ===');
    console.log('useEffect triggered, state:', state);
    console.log('state.demoSkuList:', state.demoSkuList);
    console.log('state.demoSkuList 长度:', state.demoSkuList?.length);

    const transformedData = transformDemoSkuList(state.demoSkuList);
    setShowDemoSkuList(transformedData);
    console.log('=== useEffect 执行完成 ===');
  }, [state]);

  console.log('组件渲染时 showDemoSkuList:', showDemoSkuList);

  return (
    <PhonePreview
      width={290}
    >
      <div style={{ position: 'relative' }}>
        <div
          style={createTurnTableMainStyles(main)}
          className={styles.main}
        >
          <img className={styles.kv} src={main.kv} alt="" />
          <div className={styles.btns}>
            <div>活动规则</div>
            <div>我的订单</div>
            <div>兑换记录</div>
          </div>

          <div className={styles.userInfoBg}>
            <div className={styles.userInfo}>
              <div>XXXXXX用户，您符合转段福利申领资格！</div>
              <div>请点击下方参与活动</div>
            </div>
          </div>

          <div className={styles.step1Bg}>
            <div>XXXXXXXX</div>
            <img src="https://img10.360buyimg.com/imgzone/jfs/t1/310039/3/17886/40981/6879b1a0Fc1e18cab/58c890888a115358.png" alt="" className={styles.tabContainer} />
          </div>
          <div className={styles.step2Bg}>
            {
              showDemoSkuList.map((goodsLineGroup: any, groupIndex: number) => {
                console.log('渲染商品分组:', goodsLineGroup, '索引:', groupIndex);
                return (
                  <div key={groupIndex} className={styles.step2ItemBox}>
                    <div className={styles.actNum}>{groupIndex + 1}</div>
                    <div className={styles.actTitle}>{goodsLineGroup.actTitle}</div>
                    <div className={styles.itemBox}>
                      {
                          goodsLineGroup.skuList.map((sku: any, skuIndex: number) => {
                            return (
                              <>
                                <div key={skuIndex} className={styles.step2Item}>
                                  <img className={styles.prizeImg} src={sku.skuMainPicture} alt="" />
                                  <div className={styles.getPrizeBtn}>
                                    {/* <div className={styles.price}>领券{{sku.price}}元购买</div> */}
                                    <div className={styles.price}>领券购买</div>
                                  </div>
                                </div>
                              </>
                            );
                          })
                        }
                    </div>
                  </div>
                );
              })
            }
          </div>
          <div className={styles.step3Bg} />
          <div className={styles.bottomToTop} />
        </div>
      </div>
    </PhonePreview>
  );
}